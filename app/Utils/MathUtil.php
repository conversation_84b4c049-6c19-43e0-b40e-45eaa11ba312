<?php

namespace App\Utils;

/**
 * 数字工具类
 */
class MathUtil
{

    /**
     * 格式化成元
     * @param int|null $amount
     * @return string
     * <AUTHOR>
     */
    public static function formatToYuan(?int $amount): string
    {
        if (empty($amount)) {
            return '0';
        }
        return number_format($amount / 100, 2, '.', '');
    }

    /**
     * 格式化成2位小数
     * @param string|int|float|null $amount
     * @return string
     * <AUTHOR>
     */
    public static function formatTo2DecimalPlaces(string|int|float|null $amount): string
    {
        if (is_null($amount)) {
            $amount = 0;
        }
        return number_format($amount, 2, '.', '');
    }

    /**
     * 精度数据的向上取数
     * @param $number
     * @return string
     */
    public static  function   bcceil($number):string
    {
        if (strpos($number, '.') !== false) {
            if (preg_match("~\.[0]+$~", $number)) {
                return bcround($number, 0);
            }
            if ($number[0] != '-') {
                return bcadd($number, 1, 0);
            }
            return bcsub($number, 0, 0);
        }
        return $number;
    }

    /**
     * 精度数据的向下取数
     * @param $number
     * @return string
     */
    public static  function bcfloor($number):string
    {
        if (strpos($number, '.') !== false) {
            if (preg_match("~\.[0]+$~", $number)) {
                return bcround($number, 0);
            }

            if ($number[0] != '-') {
                return bcadd($number, 0, 0);
            }

            return bcsub($number, 1, 0);
        }
        return $number;
    }

    /**
     * 精度数据的四舍五入
     * @param $number
     * @param $precision
     * @return mixed|string
     */
    public static function bcround($number, $precision = 0)
    {
        if (strpos($number, '.') !== false) {
            if ($number[0] != '-') {
                return bcadd($number, '0.' . str_repeat('0', $precision) . '5', $precision);
            }

            return bcsub($number, '0.' . str_repeat('0', $precision) . '5', $precision);
        }

        return $number;
    }

    /**
     * 精度数据的加法,支持多个数相加
     * 中间结果不做四舍五入，结果四舍五入
     * @param array $numbers
     * @param int $scale
     * @return string
     */
    public static function bcadds(array $numbers, int $scale = 2): string
    {
        $result = '0';
        foreach ($numbers as $number) {
            $result = bcadd($result, $number);
        }
        return self::bcround($result, $scale);
    }
}