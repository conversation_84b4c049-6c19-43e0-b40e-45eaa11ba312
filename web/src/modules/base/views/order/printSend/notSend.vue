<script setup>
import {
  batchAssignSupplier,
  batchDelivery,
  batchMergeOrder,
  batchSplitOrder,
  getOrderList,
  queryCanMergeList, splitUndeliveredOrders
} from "~/base/api/order.js";
import SourceSelect from "~/base/views/components/sourceSelect.vue";
import {ElInput, ElMessageBox} from "element-plus";
import TimeRangeSelect from "~/base/views/components/timeRangeSelect.vue";
import GoodsCombinSearch from "~/base/views/components/goodsCombinSearch.vue";
import SkuCombinSearch from "~/base/views/components/skuCombinSearch.vue";
import {TYPE_LABEL, TYPE_TEMPLATE_TYPE} from "@/utils/commonData.js";
import {useMessage} from "@/hooks/useMessage.js";
import FactorySelect from "~/base/views/components/factorySelect.vue";
import {commonCompanyList, platformTemplateMap} from "@/utils/expressData.js";
import RegionFilter from "~/base/views/components/regionFilter.vue";
import GoodsFilter from "~/base/views/components/goodsFilter.vue";
import useCache from '@/hooks/useCache.ts'
import {getUserSetting,saveUserSetting} from '~/base/api/user.js'

const cache = useCache()
const phone = useUserStore().getUserInfo().phone
const orderList = ref([])
const expandedRows = ref([])

const searchItem = [
  {
    label: '订单来源', prop: 'sourceId', render: (model) => {
      return h(SourceSelect, {
        modelValue: model.formData.sourceId,
        includeMerchant: true,
        'onUpdate:modelValue': (value) => {
          model.formData.sourceId = value;
        }
      });
    }
  },
  {
    label: '时间筛选', prop: 'timeRange', render: (model) => {
      return h(TimeRangeSelect, {
        modelValue: model.formData.timeRange,
        source: 'not-send',
        originalTimeField: 'order_created_at',
        'onUpdate:modelValue': (value) => {
          model.formData.timeRange = value;
        }
      });
    }
  },
  {
    label: '区域筛选', prop: 'areaId', render: () => RegionFilter
  },
  {
    label: '商品筛选', prop: 'goods', render: () => GoodsCombinSearch
  },
  {
    label: '规格筛选', prop: 'sku', render: () => SkuCombinSearch
  },
]
const DateFormat = window.DateFormat;

const shopList = useUserStore().getShops()
const loading = ref(false)
function getData(params) {
  let searchParam = {
    page: pageReq.value.page,
    page_size: pageReq.value.pageSize,
    list_type: 1
  }
  if (params) {
    searchParam = {...searchParam, ...params}
  }
  if (params?.timeRange) {
    searchParam.timeField = params.timeRange.timeField
    const now = new Date();
    if (params.timeRange.timeRange?.length) {
      searchParam.begin_at = DateFormat.format.date(
        params.timeRange.timeRange[0] || new Date(Date.now() - 90 * 86400000),
        "yyyy-MM-dd HH:mm:ss"
      );
      searchParam.end_at = DateFormat.format.date(
        params.timeRange.timeRange[1] || new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59),
        "yyyy-MM-dd HH:mm:ss"
      );
      delete searchParam.timeRange
    }
  }
  loading.value = true
  getOrderList(searchParam).then(res => {
    res.data.list.forEach((it,idx) => {
      //按下单时间排序
      it.items.sort((a, b) => a.order_created_at.localeCompare(b.order_created_at))
      const first = it.items[0]
      const firstOrder = first.order
      it.tid = first.tid
      it.id = idx
      it.type = firstOrder.type
      it.sourceName = TYPE_LABEL[firstOrder.type]
      it.shop_title = firstOrder.shop_title
      it.order_created_at = firstOrder.order_created_at
      it.promise_ship_at = firstOrder.promise_ship_at

      it.receiverInfo = firstOrder.receiver_name + ',' + (first.order_cipher_info?.receiver_phone_mask || firstOrder.receiver_phone) + ','
        + firstOrder.receiver_province + firstOrder.receiver_city + firstOrder.receiver_district + (firstOrder.receiver_town || '') + firstOrder.receiver_address
      //orders根据tid聚合
      const orderMap = {}, goodsList = [], buyer_messages = [], seller_memos = []
      const tids = [...new Set(it.items.map(it => it.tid))]
      it.tids = tids
      it.items.forEach(i => {
        if (i.order.buyer_message && !buyer_messages.find(item => item === i.order.buyer_message)) {
          buyer_messages.push(i.order.buyer_message)
        }
        if (i.order.seller_memo && !seller_memos.find(item => item === i.order.seller_memo)) {
          seller_memos.push(i.order.seller_memo)
        }
        const order = orderMap[i.tid] || {
          tid: i.tid,
          order_created_at: i.order_created_at,
          lastShipTime: i.order.promise_ship_at,
          goods: []
        }
        order.goods.push({...i})
        orderMap[i.tid] = order
        const match = goodsList.find(item => i.sku_id && item.sku_id === i.sku_id || i.product_sku_id && i.product_sku_id === item.product_sku_id)
        if (match) {
          match.sku_num += i.sku_num
        } else {
          goodsList.push({...i})
        }
      })
      it.goodsList = goodsList
      it.orders = Object.values(orderMap)
      it.buyer_message = buyer_messages.join()
      it.seller_memo = seller_memos.join()
    })
    orderList.value = res.data.list
    pageReq.value.total = res.data.total
  }).finally(() => {
    loading.value = false
  })
}

function getTotalAmount(item) {
  let amount = 0;
  item.goods.forEach(it => {
    amount += Number(it.payment)
  })
  return amount.toFixed(2);
}

const pageReq = ref({
  page: 1,
  pageSize: 100,
  total: 1
})

const handleSizeChange = (val) => {
  pageReq.value.pageSize = val
  getData();
}
const handleCurrentChange = (val) => {
  pageReq.value.page = val
  getData();
}

function init(){
  pageReq.value.page = 1
  getData();
}

defineExpose({init});

const tableRef = ref()

const toggleExpand = (row) => {
  if (expandedRows.value.includes(row.id)) {
    expandedRows.value = expandedRows.value.filter(it => it !== row.id)
  } else {
    expandedRows.value.push(row.id)
  }
  tableRef.value.toggleRowExpansion(row);
};

onMounted(() => {
  init()
  getUserMergeOrderSetting()
})
const autoMerge = ref(true)
async function getUserMergeOrderSetting(){
  const res = await getUserSetting()
  autoMerge.value = res.data.merge_switch === 1
}

const sortList = [
  {value: "order_created_time,desc", label: "下单时间从近到远", order: "desc"},
  {value: "order_created_time,asc", label: "下单时间从远到近", order: "asc"},
  {value: "pay_time,desc", label: "付款时间从近到远", order: "desc"},
  {value: "pay_time,asc", label: "付款时间从远到近", order: "asc"},
  {value: "promise_ship_at,desc", label: "剩余发货时间从小到大", order: "desc"},
  {value: "take_waybill_at,desc", label: "取号时间从近到远", order: "desc"},
  {value: "take_waybill_at,asc", label: "取号时间从远到近", order: "asc"},
  {value: "printed_time,desc", label: "打印时间从近到远", order: "desc"},
  {value: "printed_time,asc", label: "打印时间从远到近", order: "asc"},
  {value: "goods_count,desc", label: "商品数量从多到少", order: "desc"},
  {value: "goods_count,asc", label: "商品数量从少到多", order: "asc"},
  {value: "goods_price,desc", label: "商品金额从大到小", order: "desc"},
  {value: "goods_price,asc", label: "商品金额从小到大", order: "asc"},
  {value: "goods_title,asc", label: "商品名称", order: "asc"},
  {value: "custom_title,asc", label: "商品简称", order: "asc"},
  {value: "outer_iid,asc", label: "商品编码", order: "asc"},
  {value: "sku_value,asc", label: "规格名称", order: "asc"},
  {value: "custom_sku_value,asc", label: "规格简称", order: "asc"},
  {value: "outer_sku_iid,asc", label: "规格编码", order: "asc"},
  {value: "area,asc", label: "省份城市", order: "asc"}
]

const sortColumn = ref({
  field: 'order_created_time,desc',
  sort: 'desc'
})

const modalObj = ref({
  visible: false,
  title: '',
  width: '30%',
  form: {},
  type: '',
})
const msg = useMessage()
async function asyncOk() {
  switch (modalObj.value.type) {
    case 'splitOrder':
      if (!modalObj.value.form.splitList?.length) {
        msg.error('请至少拆出一笔订单')
        return
      }
      const content = !modalObj.value.form.list.length ?
        `您将${modalObj.value.form.splitList.length}笔订单全部拆出，确认操作吗？` : `您将拆出${modalObj.value.form.splitList.length}笔订单，确认操作吗？`
      msg.confirm(content, '温馨提示')
        .then(() => {
          batchSplitOrder({list: modalObj.value.form.splitList}).then(res => {
            msg.success('操作成功')
            modalObj.value.visible = false
            setTimeout(() => {
              getData()
            }, 1000)
          })
        })
      return;
    case 'dropShipping':
      if (!modalObj.value.form.supplierId) {
        msg.error('请选择厂家')
        return
      }
      batchAssignSupplier({supplier_id: modalObj.value.form.supplierId, order_item_id_arr: modalObj.value.form.list})
        .then(() => {
          msg.success('分配成功')
          modalObj.value.visible = false
          setTimeout(() => {
            getData()
          }, 1000)
        })
      return;
    case 'mergeOrder':
      const rows = mergeOrderRef.value.getSelectionRows()
      if (modalObj.value.form.ids.length === rows.length) {
        msg.error('请选择需要合单的订单')
        return
      }
      const params = {
        itemIdArr: rows.map(it => it.id),
        targetItemId: modalObj.value.form.ids[0]
      }
      batchMergeOrder({list: [params]}).then(() => {
        msg.success('操作成功')
        modalObj.value.visible = false
        setTimeout(() => {
          getData()
        }, 1000)
      })
      return
    case 'mergeOrderSet':
      if (modalObj.value.form.autoMerge === autoMerge.value) {
        modalObj.value.visible = false
        return
      }
      await saveUserSetting({merge_switch: modalObj.value.form.autoMerge ? 1: 0})
      msg.success(`自动合单已${modalObj.value.form.autoMerge ? '开启' : '关闭'}`)
      modalObj.value.visible = false
      autoMerge.value = modalObj.value.form.autoMerge
      if (autoMerge.value) {
        setTimeout(() => {
            getData()
          }, 1000)
        return
      }
      ElMessageBox.confirm(`<p>是否对已经合并的未发货订单自动拆分成未合并状态？</p><p class="red">注意：订单量超过5000单后，无法进行再次合并，请慎重拆分订单。</p>`, '拆分订单提醒', {
        confirmButtonText: '确认拆分',
        cancelButtonText: '取消',
        type: 'warning',
        dangerouslyUseHTMLString: true
      }).then(() => {
        splitUndeliveredOrders().then(()=>{
          getData()
        })
      }).catch((action) => {
        getData()
      })
      return
  }
}


function setAllocationRule() {
  modalObj.value = {
    visible: true,
    title: '自动分配设置',
    width: '30%',
    form: {},
    type: 'setAllocation',
  }
}

const shopBindColumns = [
  {label: '序号', prop: 'index'},
  {label: '平台', prop: 'platform'},
  {label: '店铺名称', prop: 'shopName'},
  {label: '厂家', prop: 'factory'},
]

function setShopBindFactory() {
  modalObj.value = {
    visible: true,
    title: '店铺绑定厂家',
    width: '50%',
    form: {},
    type: 'shopBindFactory',
  }
}

const goodsList = ref([
  {
    platform: '抖音',
    goodsName: '天然室内印尼水沉香盘香 家用卧室2小时安神助眠熏香',
    skuName: '1盒;'
  }, {
    platform: '抖音',
    goodsName: '1现货日版CHAMPION冠军潮牌双肩背包男女小清新情侣款翻盖书包17S',
    skuName: '香槟色;'
  }, {
    platform: '抖音',
    goodsName: '1现货日版CHAMPION冠军潮牌双肩背包男女小清新情侣款翻盖书包17S',
    skuName: '浅紫色;'
  }, {
    platform: '抖音',
    goodsName: '1现货日版CHAMPION冠军潮牌双肩背包男女小清新情侣款翻盖书包17S',
    skuName: '紫罗兰;'
  }])
const excludeGoodsColumns = [
  {label: '平台', prop: 'platform'},
  {label: '商品名称', prop: 'goodsName'},
  {label: '规格名称', prop: 'skuName'},
  {label: '操作', prop: 'opt'},
]

function removeGoods(idx) {
  goodsList.value.splice(idx, 1)
}

function setExcludeGoodsRule() {
  modalObj.value = {
    visible: true,
    title: '开启整店推送，设置部分商品不推送',
    width: '70%',
    form: {},
    type: 'excludeGoodsRule',
  }
}

const mergeOrderFlag = ref(true)

function setMergeOrderRule() {
  modalObj.value = {
    visible: true,
    title: '自动合单',
    width: '30%',
    form: {autoMerge: autoMerge.value},
    type: 'mergeOrderSet',
  }
}

const {
  getMenuCollapseState
} = useSettingStore()

const selectRowCount = computed(() => {
  if (!tableRef.value) {
    return 0;
  }
  return tableRef.value.getSelectionRows().length
})

const splitOrderRef = ref()

function splitOrder(order) {
  modalObj.value = {
    visible: true,
    title: '拆分订单',
    width: '800px',
    form: {list: [...order.items], splitList: []},
    type: 'splitOrder',
  }
}

function commitSplit(row, index) {
  modalObj.value.form.splitList.push({itemIdArr: [row.id]})
  modalObj.value.form.list.splice(index, 1)
}

function batchCommitSplit() {
  const rows = splitOrderRef.value.getSelectionRows()
  if (!rows.length) {
    msg.error('请选择需要拆出的订单')
    return
  }
  let ids = rows.map(it => it.id)
  ids.forEach(it => {
    modalObj.value.form.splitList.push({itemIdArr: [it]})
  })
  modalObj.value.form.list = modalObj.value.form.list.filter(it => !ids.includes(it.id))
}

const mergeOrderRef = ref()

async function mergeOrder(row) {
  const res = await queryCanMergeList({order_item_id: row.items[0].id})
  const ids = [...row.items.map(it => it.id)]
  const list = res.data.filter(it => ids.includes(it.id))
  list.push(...res.data.filter(it => !ids.includes(it.id)))
  //先找出哪些订单可以合单
  modalObj.value = {
    visible: true,
    title: '合并订单',
    width: '800px',
    form: {list, ids},
    type: 'mergeOrder',
  }
  nextTick(() => {
    modalObj.value.form.list.forEach(row => {
      if (ids.includes(row.id)) {
        mergeOrderRef.value.toggleRowSelection(row, true);
      }
    });
  });
}

function mergeCanChoose(row) {
  return !modalObj.value.form.ids.includes(row.id)
}

function dropShipping() {
  let rows = tableRef.value.getSelectionRows()
  if (!rows.length) {
    msg.error('请选择订单')
    return
  }
  //剔除分配的订单
  rows = rows.filter(it=>!it.tags.is_supplier_order)
  if (!rows.length) {
    msg.error('请选择非分销的订单')
    return
  }
  const list = []
  rows.forEach(it => {
    list.push(...it.items.map(i => i.id))
  })
  modalObj.value = {
    visible: true,
    title: '分配订单',
    width: '400px',
    form: {list},
    type: 'dropShipping',
  }
}

const emit = defineEmits(['sendByManual']);
function showSendByManual() {
  const rows = tableRef.value.getSelectionRows()
  if (!rows.length) {
    msg.error('请选择订单')
    return
  }
  emit('sendByManual',rows)
}

</script>

<template>
  <div>
    <div class="not-send-order">
      <div class="order-search">
        <maSearch
          :options="{ showButton: true }"
          :form-options="{ labelWidth: '80px'}"
          :search-items="searchItem"
          @search="getData"
        />
      </div>
      <div class="order-table">
        <div class="order-opt-content flex_center">
          <div class="ml-auto flex_center">
            <a @click="setAllocationRule">自动分配设置</a>
            <a class="ml-8" @click="setMergeOrderRule">合单设置</a>
            <el-select class="ml-8" style="width: 200px" v-model="sortColumn.field">
              <el-option v-for="it in sortList" :key="it.value" :label="it.label" :value="it.value"/>
            </el-select>
          </div>
        </div>
        <el-table :data="orderList" row-key="id" ref="tableRef" :expand-row-keys="expandedRows" v-loading="loading">
          <el-table-column width="40" type="selection"/>
          <el-table-column width="1" type="expand" class-name="hidden-expand-icon">
            <template #default="scope">
              <div class="expand-content">
                <el-card v-for="(it,idx) in scope.row.orders" :key="idx" class="mb-2">
                  <template #header>
                    <div class="flex_center">
                      <span>订单号：</span>
                      <span>{{ it.tid }}</span>
                      <span class="ml-5">下单时间：</span>
                      <span>{{ it.order_created_at }}</span>
                      <span class="ml-5">承诺揽收时间：</span>
                      <span>{{ it.lastShipTime }}</span>
                    </div>
                  </template>
                  <div class="flex">
                    <div>
                      <div v-for="(item,idx2) in it.goods" :key="idx2" class="flex_center mb-2">
                        <el-image style="width: 60px; height: 60px;min-width: 60px" :src="item.sku_pic"
                                  :hide-on-click-modal="true" :preview-src-list="[it.sku_pic]"/>
                        <div class="ml-3">
                          <p>{{ item.goods_title }}</p>
                          <p>{{ item.sku_value }}</p>
                        </div>
                        <div class="ml-5">
                          <span>¥{{ item.sku_price }}</span>
                          <span class="ml-2"> X {{ item.sku_num }}</span>
                        </div>
                      </div>
                    </div>
                    <div class="right_price flex_center">
                      <div class="red">¥ {{ getTotalAmount(it) }}</div>
                    </div>
                  </div>
                </el-card>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="标签" :show-overflow-tooltip="false">
            <template #default="scope">
              <el-tag v-if="scope.row.tags.is_merge" type="danger" class="mr-1 mb-1">合</el-tag>
              <el-tag v-if="scope.row.tags.is_split" type="danger" class="mr-1 mb-1">拆</el-tag>
              <el-tag v-if="scope.row.tags.is_mergeable" type="warning" class="mr-1 mb-1">待合</el-tag>
              <el-tag v-if="scope.row.tags.is_supplier_order" type="primary" class="mr-1 mb-1">分销</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="订单来源" :show-overflow-tooltip="false" prop="source">
            <template #default="scope">
              <p>【{{ scope.row.sourceName || '自定义' }}】</p>
              <p>{{ scope.row.shop_title }}</p>
            </template>
          </el-table-column>
          <el-table-column label="系统单号" :show-overflow-tooltip="false" prop="system_order_no"/>
          <el-table-column label="收件信息" :show-overflow-tooltip="false" prop="receiverInfo"/>
          <el-table-column label="留言备注" :show-overflow-tooltip="false" prop="remark">
            <template #default="scope">
              <p>留言: {{ scope.row.buyer_message }}</p>
              <p>备注: {{ scope.row.seller_memo }}</p>
            </template>
          </el-table-column>
          <el-table-column label="物流公司" :show-overflow-tooltip="false">
            <template #default="scope">
              <el-select v-model="scope.row.smartLogistic" filterable  placeholder="快递公司">
                <el-option v-for="it in commonCompanyList" :key="it.wp_code" :label="it.name" :value="it.wp_code"/>
              </el-select>
            </template>
          </el-table-column>
          <el-table-column label="运单号" :show-overflow-tooltip="false" prop="waybill_code"/>
          <el-table-column label="商品" :show-overflow-tooltip="false" min-width="200">
            <template #default="scope">
              <div class="flex_center mb-2" v-for="it in scope.row.goodsList" :key="it.skuId">
                <el-image
                  v-if="it.sku_pic"
                  style="width: 60px; height: 60px;min-width: 60px"
                  :src="it.sku_pic"
                  :hide-on-click-modal="true"
                  :preview-src-list="[it.sku_pic]"
                />
                <div class="ml-2">
                  <p>{{ it.goods_title }}</p>
                  <p>{{ it.sku_value }}</p>
                  <p>X {{ it.sku_num }}</p>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="下单时间/承诺揽收时间" :show-overflow-tooltip="false" width="170">
            <template #default="scope">
              <p>{{ scope.row.order_created_at }}</p>
              <p>{{ scope.row.promise_ship_at }}</p>
            </template>
          </el-table-column>
          <el-table-column label="操作" :show-overflow-tooltip="false" width="100">
            <template #default="scope">
              <a class="mr-2" @click="toggleExpand(scope.row)">
                {{ expandedRows.includes(scope.row.id) ? '收起' : '详情' }}
              </a>
              <a class="mr-2" v-if="scope.row.itemCount > 1" @click="splitOrder(scope.row)">拆单</a>
              <a @click="mergeOrder(scope.row)" v-if="scope.row.tags.is_mergeable">合单</a>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <div class="order-footer flex_center"
         :style="{'width': getMenuCollapseState() ? 'calc(100% - var(--mine-g-sub-aside-collapse-width) - 25px)' : 'calc(100% - var(--mine-g-sub-aside-width) - 25px)'}">
      <div>
        <span>已选</span>
        <span class="order_count">{{ selectRowCount }}</span>
        <span>笔</span>
      </div>
      <div class="ml-10">
        <el-button type="primary">打快递单</el-button>
        <el-button>打发货单</el-button>
        <el-popover placement="top" popper-style="text-align:center">
          <template #reference>
            <el-button>
              <span>发货</span>
              <ma-svg-icon name="material-symbols:keyboard-arrow-up" :size="20"/>
            </el-button>
          </template>
          <template #default>
            <div class="pointer" @click="showSendByManual">输入运单号发货</div>
          </template>
        </el-popover>

        <el-button>选择/修改物流</el-button>
        <el-button @click="dropShipping">转为代发</el-button>
        <!--        <el-button>拆合单</el-button>-->
        <el-button>回收单号</el-button>
        <el-button>备注</el-button>
        <el-button>锁定订单</el-button>
      </div>
      <div class="ml-auto">
        <el-pagination
          v-model:current-page="pageReq.page"
          v-model:page-size="pageReq.pageSize"
          :page-sizes="[100, 200, 300, 400]"
          layout="total, sizes, prev, pager, next"
          :total="pageReq.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
    <el-dialog :title="modalObj.title" v-model="modalObj.visible" :width="modalObj.width" :close-on-click-modal="false">
      <template v-if="modalObj.type === 'splitOrder'">
        <el-table :data="modalObj.form.list" :max-height="600" ref="splitOrderRef" max-height="600" empty-text="订单已全部拆出">
          <el-table-column width="40" type="selection"/>
          <el-table-column label="商品信息" prop="goods_title"/>
          <el-table-column label="规格信息" prop="sku_value"/>
          <el-table-column label="数量" prop="sku_num" width="80"/>
          <el-table-column label="操作" width="80">
            <template #default="scope">
              <a @click="commitSplit(scope.row, scope.$index)">拆出</a>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <template v-else-if="modalObj.type === 'mergeOrder'">
        <p>有 {{modalObj.form.list.length}} 笔可合并订单，已选 {{mergeOrderRef?.getSelectionRows()?.length}} 笔</p>
        <el-table ref="mergeOrderRef" class="mt-5" max-height="600" :data="modalObj.form.list">
          <el-table-column width="40" type="selection" :selectable="mergeCanChoose"/>
          <el-table-column label="订单号" prop="tid"/>
          <el-table-column label="商品信息">
            <template #default="scope">
              <div class="flex_center">
                <el-image
                  v-if="scope.row.sku_pic"
                  style="width: 60px; height: 60px;min-width: 60px"
                  :src="scope.row.sku_pic"
                  :hide-on-click-modal="true"
                  :preview-src-list="[scope.row.sku_pic]"
                />
                <div class="ml-2">
                  <p>{{ scope.row.goods_title }}</p>
                  <p>{{ scope.row.sku_value }}</p>
                  <p>X {{ scope.row.sku_num }}</p>
                </div>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </template>
      <template v-else-if="modalObj.type === 'dropShipping'">
        <div class="flex_center">
          <span style="min-width: 80px">选择厂家：</span>
          <factory-select v-model="modalObj.form.supplierId"/>
        </div>
      </template>
      <template v-else-if="modalObj.type === 'setAllocation'">
        <div class="flex_center">
          <span>自动分配订单</span>
          <el-switch class="ml-3" active-text="已开启" inactive-text="已关闭" inline-prompt/>
        </div>
        <p class="red mt-1">开启自动分配后，厂家可直接同步到应分配给他的订单</p>
        <div class="flex_center mt-5">
          <el-checkbox>按商品所属关系自动分配给厂家</el-checkbox>
          <a class="ml-2">商品设置</a>
        </div>
        <div class="flex_center">
          <el-checkbox>按店铺推送给厂家</el-checkbox>
          <a class="ml-2" @click="setShopBindFactory">设置店铺绑定厂家</a>
          <a class="ml-2" @click="setExcludeGoodsRule">设置部分商品不推</a>
        </div>
        <div class="flex_center">
          <span>赠品跟随其他商品推送厂家</span>
          <el-switch class="ml-3" active-text="已开启" inactive-text="已关闭" inline-prompt/>
          <el-tooltip placement="top" effect="light">
            <template #content>
              <p>
                开启后<br/>
                只有一个商品，赠品优先跟随商品推送厂家，不按赠品绑定厂家推送；<br/>
                存在多个商品，赠品按绑定厂家推送。
              </p>
            </template>
            <ma-svg-icon name="material-symbols:help" class="ml-1 question-img"/>
          </el-tooltip>
        </div>
      </template>
      <template v-else-if="modalObj.type === 'shopBindFactory'">
        <ma-table :data="shopList" :columns="shopBindColumns">
          <template #column-index="scope">
            <p>{{ scope.$index + 1 }}</p>
          </template>
          <template #column-factory="scope">
            <el-select/>
          </template>
        </ma-table>
      </template>
      <template v-else-if="modalObj.type === 'excludeGoodsRule'">
        <el-button type="primary" size="small">添加商品</el-button>
        <ma-table :data="goodsList" :columns="excludeGoodsColumns" class="mt-2">
          <template #column-opt="scope">
            <a class="red" @click="removeGoods(scope.$index)">删除</a>
          </template>
        </ma-table>
      </template>
      <template v-else-if="modalObj.type === 'mergeOrderSet'">
        <div class="flex_center">
          <span>自动合单</span>
          <el-switch class="ml-3" active-text="已开启" inactive-text="已关闭" inline-prompt
                     v-model="modalObj.form.autoMerge"/>
        </div>
        <p class="red mt-1" v-if="!modalObj.form.autoMerge">关闭自动合单后功能后，之后同步进来同一买家地址的订单将不会进行自动合并，由此您可能会额外付出不必要的快递费。</p>
        <p class="mt-1 grey" v-if="modalObj.form.autoMerge">
          <span>收件人姓名、买家标识、电话、收件地址完全相同才合并。</span>
          <span class="red">当手动开启自动合单时，只对新订单生效。</span>
        </p>
      </template>
      <template #footer>
        <el-button style="float: left" v-if="modalObj.type === 'splitOrder'" type="primary" plain
                   @click="batchCommitSplit">批量拆出
        </el-button>
        <el-button @click="modalObj.visible = false">取 消</el-button>
        <el-button type="primary" @click="asyncOk">确 定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style lang="scss">
.hidden-expand-icon .el-table__expand-icon {
  display: none;
}

.hidden-expand-icon {
  width: 0;
  padding: 0;
}

.order-footer {
  height: 60px;
  position: fixed;
  bottom: 0;
  background: white;
  z-index: 111;
  right: 0;
  padding: 0 1rem;
  column-gap: 0.25rem;

  .order_count {
    margin: 0 0.2rem;
    font-size: 20px;
    color: var(--el-color-primary);
  }
}

.not-send-order {
  margin: 0.75rem;

  .expand-content {
    padding: 0.5rem;

    .right_price {
      width: 120px;
      border-left: 1px solid #ccc;
      margin-left: auto;
      padding-left: 1rem;
    }
  }

  .sort_item {
    cursor: pointer;
    width: 24px;
    height: 24px;
    line-height: 24px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #ccc;
    transition: all .5s;
    color: #ccc;
  }

  .sort_checked {
    color: #fff;
    border: none;
    background: var(--el-color-primary);
  }

  .order-search {
    background: white;
    padding: 0.75rem 0.75rem 0 0.75rem;
    margin: 0.75rem 0;
  }

  .order-table {
    background: white;
    padding: 0 0.75rem 5rem 0.75rem;
    margin: 0.75rem 0;

    .order-opt-content {
      height: 60px;
      line-height: 30px;
      font-size: 14px;
    }
  }

  .tab-select {
    background: white;
    padding: 0.75rem;
  }

  .opt-button {
    position: absolute;
    right: 1rem;
    top: 0.75rem;
  }
}

.question-img {
  font-size: 20px;
  cursor: pointer;
}
</style>
