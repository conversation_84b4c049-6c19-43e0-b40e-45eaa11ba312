<script setup>
import TimeRangeSelect from "~/base/views/components/timeRangeSelect.vue";
import SourceSelect from "~/base/views/components/sourceSelect.vue";
import {useMessage} from "@/hooks/useMessage.js";
import {getOrderList} from "~/base/api/order.js";
import {TYPE_LABEL} from "@/utils/commonData.js";
import {commonCompanyList, wpCode2UnionWpCode} from "@/utils/expressData.js";
import RegionFilter from "~/base/views/components/regionFilter.vue";
import GoodsCombinSearch from "~/base/views/components/goodsCombinSearch.vue";
import SkuCombinSearch from "~/base/views/components/skuCombinSearch.vue";

const orderType = ref(1)
const typeList = [
  {label: '地址变更', value: 1},
  {label: '发生售后', value: 2},
  {label: '订单关闭', value: 3},
  {label: '外部发货', value: 4},
]

const searchItem = [
  {
    label: '订单来源', prop: 'sourceId', render: (model) => {
      return h(SourceSelect, {
        modelValue: model.formData.sourceId,
        includeMerchant: true,
        includeFactory: true,
        'onUpdate:modelValue': (value) => {
          model.formData.sourceId = value;
        }
      });
    }
  },
  {
    label: '时间筛选', prop: 'timeRange', render: (model) => {
      return h(TimeRangeSelect, {
        modelValue: model.formData.timeRange,
        source: 'has-send',
        originalTimeField: 'order_created_at',
        'onUpdate:modelValue': (value) => {
          model.formData.timeRange = value;
        }
      });
    }
  },
  {
    label: '区域筛选', prop: 'areaId', render: () => RegionFilter
  },
  {
    label: '商品筛选', prop: 'goods', render: () => GoodsCombinSearch
  },
  {
    label: '规格筛选', prop: 'sku', render: () => SkuCombinSearch
  },
]

const sortList = [
  {value: "order_created_time,desc", label: "下单时间从近到远", order: "desc"},
  {value: "order_created_time,asc", label: "下单时间从远到近", order: "asc"},
  {value: "pay_time,desc", label: "付款时间从近到远", order: "desc"},
  {value: "pay_time,asc", label: "付款时间从远到近", order: "asc"},
  {value: "promise_ship_at,desc", label: "剩余发货时间从小到大", order: "desc"},
  {value: "take_waybill_at,desc", label: "取号时间从近到远", order: "desc"},
  {value: "take_waybill_at,asc", label: "取号时间从远到近", order: "asc"},
  {value: "printed_time,desc", label: "打印时间从近到远", order: "desc"},
  {value: "printed_time,asc", label: "打印时间从远到近", order: "asc"},
  {value: "goods_count,desc", label: "商品数量从多到少", order: "desc"},
  {value: "goods_count,asc", label: "商品数量从少到多", order: "asc"},
  {value: "goods_price,desc", label: "商品金额从大到小", order: "desc"},
  {value: "goods_price,asc", label: "商品金额从小到大", order: "asc"},
  {value: "goods_title,asc", label: "商品名称", order: "asc"},
  {value: "custom_title,asc", label: "商品简称", order: "asc"},
  {value: "outer_iid,asc", label: "商品编码", order: "asc"},
  {value: "sku_value,asc", label: "规格名称", order: "asc"},
  {value: "custom_sku_value,asc", label: "规格简称", order: "asc"},
  {value: "outer_sku_iid,asc", label: "规格编码", order: "asc"},
  {value: "area,asc", label: "省份城市", order: "asc"}
]

const sortColumn = ref({
  field: 'order_created_time,desc',
  sort: 'desc'
})


const msg = useMessage()

const orderList = ref([])
const expandedRows = ref([])

const tableRef = ref()

const toggleExpand = (row) => {
  if (expandedRows.value.includes(row.id)) {
    expandedRows.value = expandedRows.value.filter(it => it !== row.id)
  } else {
    expandedRows.value.push(row.id)
  }
  tableRef.value.toggleRowExpansion(row);
};

function getTotalAmount(item) {
  let amount = 0;
  item.goods.forEach(it => {
    amount += Number(it.payment)
  })
  return amount.toFixed(2);
}

const pageReq = ref({
  page: 1,
  pageSize: 100,
  total: 1
})

const handleSizeChange = (val) => {
  pageReq.value.pageSize = val
  getData();
}
const handleCurrentChange = (val) => {
  pageReq.value.page = val
  getData();
}

function init() {
  pageReq.value.page = 1
  getData();
}

defineExpose({init});

const DateFormat = window.DateFormat;

const shopList = useUserStore().getShops()
const loading = ref(false)
function getData(params) {
  let searchParam = {
    page: pageReq.value.page,
    page_size: pageReq.value.pageSize,
    abnormal_type: orderType.value,
    list_type: 6
  }
  if (params) {
    searchParam = {...searchParam, ...params}
  }
  if (params?.timeRange) {
    searchParam.timeField = params.timeRange.timeField
    const now = new Date();
    if (params.timeRange.timeRange?.length) {
      searchParam.begin_at = DateFormat.format.date(
        params.timeRange.timeRange[0] || new Date(Date.now() - 90 * 86400000),
        "yyyy-MM-dd HH:mm:ss"
      );
      searchParam.end_at = DateFormat.format.date(
        params.timeRange.timeRange[1] || new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59),
        "yyyy-MM-dd HH:mm:ss"
      );
      delete searchParam.timeRange
    }
  }
  loading.value = true
  getOrderList(searchParam).then(res => {
    res.data.list.forEach((it, idx) => {
      //按下单时间排序
      it.items.sort((a, b) => a.order_created_at.localeCompare(b.order_created_at))
      const first = it.items[0]
      const firstOrder = first.order
      it.tid = first.tid
      it.id = idx
      it.type = firstOrder.type
      it.sourceName = TYPE_LABEL[firstOrder.type]
      it.shop_title = firstOrder.shop_title
      it.order_created_at = firstOrder.order_created_at
      it.promise_ship_at = firstOrder.promise_ship_at

      it.receiverInfo = firstOrder.receiver_name + ',' + (first.order_cipher_info?.receiver_phone_mask || firstOrder.receiver_phone) + ','
        + firstOrder.receiver_province + firstOrder.receiver_city + firstOrder.receiver_district + (firstOrder.receiver_town || '') + firstOrder.receiver_address
      //orders根据tid聚合
      const orderMap = {}, goodsList = [], buyer_messages = [], seller_memos = [], wpCodes = [], waybillCodes = []
      const tids = [...new Set(it.items.map(it => it.tid))]
      it.tids = tids
      it.items.forEach(i => {
        if (i.order.buyer_message && !buyer_messages.find(item => item === i.order.buyer_message)) {
          buyer_messages.push(i.order.buyer_message)
        }
        if (i.order.seller_memo && !seller_memos.find(item => item === i.order.seller_memo)) {
          seller_memos.push(i.order.seller_memo)
        }
        if (i.waybill_code && !waybillCodes.find(item => item === i.waybill_code)) {
          waybillCodes.push(i.waybill_code)
        }
        if (i.wp_code && !wpCodes.find(item => item === i.wp_code)) {
          wpCodes.push(i.wp_code)
        }
        const order = orderMap[i.tid] || {
          tid: i.tid,
          order_created_at: i.order_created_at,
          lastShipTime: i.order.promise_ship_at,
          goods: []
        }
        order.goods.push({...i})
        orderMap[i.tid] = order
        const match = goodsList.find(item => i.sku_id && item.sku_id === i.sku_id || i.product_sku_id && i.product_sku_id === item.product_sku_id)
        if (match) {
          match.sku_num += i.sku_num
        } else {
          goodsList.push({...i})
        }
      })
      it.goodsList = goodsList
      it.orders = Object.values(orderMap)
      it.buyer_message = buyer_messages.join()
      it.seller_memo = seller_memos.join()
      it.wp_code = wpCodes[0]
      it.wpName = commonCompanyList.find(it => it.wp_code === wpCode2UnionWpCode[wpCodes[0]])?.name
      it.waybill_codes = waybillCodes.join()
    })
    orderList.value = res.data.list
    pageReq.value.total = res.data.total
  }).finally(() => {
      loading.value = false
    })
}

const {
  getMenuCollapseState
} = useSettingStore()

const selectRowCount = computed(() => {
  if (!tableRef.value) {
    return 0;
  }
  return tableRef.value.getSelectionRows().length
})

onMounted(() => {
  init()
})

function flagCommonOrder() {
  const rows = tableRef.value.getSelectionRows()
  if (!rows.length) {
    msg.error('请选择订单')
    return
  }
  const list = []
  rows.forEach(it => {
    list.push(...it.items.map(i => i.id))
  })
  msg.confirm(`标记后，订单将回到待发货列表`, '确认标记为正常订单？').then(() => {
    // batchCancelAssignSupplier({order_item_id_arr:list})
    //   .then(()=>{
    //     msg.success('取消分配成功')
    //     setTimeout(() => {
    //       getData()
    //     }, 1000)
    //   })
  })
}

</script>

<template>
  <div>
    <div class="special-order">
      <el-radio-group v-model="orderType" @change="init">
        <el-radio-button v-for="it in typeList" :key="it.value" :value="it.value" :label="it.label"/>
      </el-radio-group>
      <div class="order-search">
        <maSearch
          :options="{ showButton: true }"
          :form-options="{ labelWidth: '80px'}"
          :search-items="searchItem"
          @search="getData"
        />
      </div>
      <div class="order-table">
        <div class="order-opt-content flex_center">
          <div class="ml-auto flex_center">
            <el-select class="ml-8" style="width: 200px" v-model="sortColumn.field">
              <el-option v-for="it in sortList" :key="it.value" :label="it.label" :value="it.value"/>
            </el-select>
          </div>
        </div>
        <el-table :data="orderList" row-key="id" ref="tableRef" :expand-row-keys="expandedRows" v-loading="loading">
          <el-table-column width="40" type="selection"/>
          <el-table-column width="1" type="expand" class-name="hidden-expand-icon">
            <template #default="scope">
              <div class="expand-content">
                <el-card v-for="(it,idx) in scope.row.orders" :key="idx" class="mb-2">
                  <template #header>
                    <div class="flex_center">
                      <span>订单号：</span>
                      <span>{{ it.tid }}</span>
                      <span class="ml-5">下单时间：</span>
                      <span>{{ it.order_created_at }}</span>
                      <span class="ml-5">承诺揽收时间：</span>
                      <span>{{ it.lastShipTime }}</span>
                    </div>
                  </template>
                  <div class="flex">
                    <div>
                      <div v-for="(item,idx2) in it.goods" :key="idx2" class="flex_center mb-2">
                        <el-image style="width: 60px; height: 60px;min-width: 60px" :src="item.sku_pic"
                                  :hide-on-click-modal="true" :preview-src-list="[it.sku_pic]"/>
                        <div class="ml-3">
                          <p>{{ item.goods_title }}</p>
                          <p>{{ item.sku_value }}</p>
                        </div>
                        <div class="ml-5">
                          <span>¥{{ item.sku_price }}</span>
                          <span class="ml-2"> X {{ item.sku_num }}</span>
                        </div>
                      </div>
                    </div>
                    <div class="right_price flex_center">
                      <div class="red">¥ {{ getTotalAmount(it) }}</div>
                    </div>
                  </div>
                </el-card>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="标签" :show-overflow-tooltip="false">
            <template #default="scope">
              <el-tag v-if="scope.row.tags.is_merge" type="danger" class="mr-1 mb-1">合</el-tag>
              <el-tag v-if="scope.row.tags.is_split" type="danger" class="mr-1 mb-1">拆</el-tag>
              <el-tag v-if="scope.row.tags.is_mergeable" type="warning" class="mr-1 mb-1">待合</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="订单号" :show-overflow-tooltip="false" prop="tid"/>
          <el-table-column label="收件信息" :show-overflow-tooltip="false" prop="receiverInfo"/>
          <el-table-column label="留言备注" :show-overflow-tooltip="false" prop="remark">
            <template #default="scope">
              <p>留言: {{ scope.row.buyer_message }}</p>
              <p>备注: {{ scope.row.seller_memo }}</p>
            </template>
          </el-table-column>
          <el-table-column label="运单号" :show-overflow-tooltip="false" prop="waybill_code">
            <template #default="scope">
              <p>{{scope.row.wpName}}</p>
              <p>{{scope.row.waybill_codes}}</p>
            </template>
          </el-table-column>
          <el-table-column label="商品" :show-overflow-tooltip="false" min-width="200">
            <template #default="scope">
              <div class="flex_center mb-2" v-for="it in scope.row.goodsList" :key="it.skuId">
                <el-image
                  v-if="it.sku_pic"
                  style="width: 60px; height: 60px;min-width: 60px"
                  :src="it.sku_pic"
                  :hide-on-click-modal="true"
                  :preview-src-list="[it.sku_pic]"
                />
                <div class="ml-2">
                  <p>{{ it.goods_title }}</p>
                  <p>{{ it.sku_value }}</p>
                  <p>X {{ it.sku_num }}</p>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="下单时间/承诺揽收时间" :show-overflow-tooltip="false" width="170">
            <template #default="scope">
              <p>{{ scope.row.order_created_at }}</p>
              <p>{{ scope.row.promise_ship_at }}</p>
            </template>
          </el-table-column>
          <el-table-column label="操作" :show-overflow-tooltip="false" width="100">
            <template #default="scope">
              <a @click="toggleExpand(scope.row)">
                {{ expandedRows.includes(scope.row.id) ? '收起' : '详情' }}
              </a>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <div class="order-footer flex_center"
         :style="{'width': getMenuCollapseState() ? 'calc(100% - var(--mine-g-sub-aside-collapse-width) - 25px)' : 'calc(100% - var(--mine-g-sub-aside-width) - 25px)'}">
      <div>
        <span>已选</span>
        <span class="order_count">{{ selectRowCount }}</span>
        <span>笔</span>
      </div>
      <div class="ml-10">
        <template v-if="[1,2].includes(orderType)">
          <el-button type="primary" @click="flagCommonOrder">标记为正常订单</el-button>
          <el-button type="primary" plain>重打快递单</el-button>
        </template>
        <el-button @click="flagCommonOrder">回收单号</el-button>
        <el-button>备注</el-button>
      </div>
      <div class="ml-auto">
        <el-pagination
          v-model:current-page="pageReq.page"
          v-model:page-size="pageReq.pageSize"
          :page-sizes="[100, 200, 300, 400]"
          layout="total, sizes, prev, pager, next"
          :total="pageReq.total"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>


<style lang="scss">
.hidden-expand-icon .el-table__expand-icon {
  display: none;
}

.hidden-expand-icon {
  width: 0;
  padding: 0;
}

.order-footer {
  height: 60px;
  position: fixed;
  bottom: 0;
  background: white;
  z-index: 111;
  right: 0;
  padding: 0 1rem;
  column-gap: 0.25rem;

  .order_count {
    margin: 0 0.2rem;
    font-size: 20px;
    color: var(--el-color-primary);
  }
}

.special-order {
  margin: 0.75rem;

  .expand-content {
    padding: 0.5rem;

    .right_price {
      width: 120px;
      border-left: 1px solid #ccc;
      margin-left: auto;
      padding-left: 1rem;
    }
  }

  .sort_item {
    cursor: pointer;
    width: 24px;
    height: 24px;
    line-height: 24px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #ccc;
    transition: all .5s;
    color: #ccc;
  }

  .sort_checked {
    color: #fff;
    border: none;
    background: var(--el-color-primary);
  }

  .order-search {
    background: white;
    padding: 0.75rem 0.75rem 0 0.75rem;
    margin: 0.75rem 0;
  }

  .order-table {
    background: white;
    padding: 0 0.75rem 5rem 0.75rem;
    margin: 0.75rem 0;

    .order-opt-content {
      height: 60px;
      line-height: 30px;
      font-size: 14px;
    }
  }

  .tab-select {
    background: white;
    padding: 0.75rem;
  }

  .opt-button {
    position: absolute;
    right: 1rem;
    top: 0.75rem;
  }
}

.question-img {
  font-size: 20px;
  cursor: pointer;
}
</style>
