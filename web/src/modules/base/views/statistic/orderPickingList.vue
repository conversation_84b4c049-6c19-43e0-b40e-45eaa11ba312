<script setup>
import {getUserSetting} from "~/base/api/user.js";
import GoodsPickingList from "~/base/views/statistic/goodsPickingList.vue";
import ProductPickingList from "~/base/views/statistic/productPickingList.vue";

const beihuoTemplate = ref({})

onMounted(async () => {
  await getConfig()
  changeType()
})



async function getConfig() {
  const res = await getUserSetting()
  let beihuoContents = {}
  if (res.data?.stock_config) {
    beihuoContents = JSON.parse(res.data.stock_config)
  }
  beihuoTemplate.value = beihuoContents
}

const groupType = ref(1)
const typeList = [
  {label: '平台商品维度', value: 1},
  {label: '系统货品维度', value: 2}
]

const goodsPickingRef =ref()
const productPickingRef =ref()

function changeType(){
  switch (groupType.value) {
    case 1:
      goodsPickingRef.value.init(beihuoTemplate['goods'] || {})
      break;
    case 2:
      productPickingRef.value.init(beihuoTemplate['product'] || {})
      break;
   default:
      break;
  }
}


</script>

<template>
  <div>
    <div class="order-picking-list">
      <el-radio-group v-model="groupType" @change="changeType">
        <el-radio-button v-for="it in typeList" :key="it.value" :value="it.value" :label="it.label"/>
      </el-radio-group>
      <goods-picking-list v-if="groupType === 1" ref="goodsPickingRef"/>
      <product-picking-list v-if="groupType === 2" ref="productPickingRef"/>
    </div>
  </div>
</template>

<style lang="scss">
.order-picking-list {
  margin: 0.75rem;
}
</style>
