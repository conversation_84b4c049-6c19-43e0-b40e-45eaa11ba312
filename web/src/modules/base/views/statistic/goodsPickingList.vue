<script setup>
import SourceSelect from "~/base/views/components/sourceSelect.vue";
import GoodsFilter from "~/base/views/components/goodsFilter.vue";
import TimeRangeSelect from "~/base/views/components/timeRangeSelect.vue";
import RegionFilter from "~/base/views/components/regionFilter.vue";
import {ArrowDown, ArrowUp} from '@element-plus/icons-vue'
import GoodsSetConfig from "~/base/views/statistic/components/GoodsSetConfig.vue";
import SkuSetConfig from "~/base/views/statistic/components/SkuSetConfig.vue";
import {getUserSetting, saveUserSetting} from "~/base/api/user.js";


const searchItem = [
  {
    label: '订单来源', prop: 'sourceId', render: (model) => {
      return h(SourceSelect, {
        modelValue: model.formData.sourceId,
        includeMerchant: true,
        'onUpdate:modelValue': (value) => {
          model.formData.sourceId = value;
        }
      });
    }
  },
  {
    label: '商品筛选', prop: 'goods', render: () => GoodsFilter
  },
  {
    label: '时间筛选', prop: 'timeRange', render: (model) => {
      return h(TimeRangeSelect, {
        modelValue: model.formData.timeRange,
        source: 'not-send',
        originalTimeField: 'order_created_at',
        'onUpdate:modelValue': (value) => {
          model.formData.timeRange = value;
        }
      });
    }
  },
  {
    label: '区域筛选', prop: 'areaId', render: () => RegionFilter
  }
]

const tableData = ref([])
const loading = ref(false)

function getData(params) {

}

const skuSortList = [{label: '按规格数量从大到小排序', value: 'skuCount desc'},
  {label: '按规格数量从小到大排序', value: 'skuCount asc'},
  {label: '按规格简称排序', value: 'skuAlias asc'},
  {label: '按规格简称(无简称时按照名称)排序', value: 'skuTitle asc'},
  {label: '按规格编码排序', value: 'outerSkuIid asc'},
  {label: '按下单时间早的在前排序', value: 'payAtMin asc'},
  {label: '按下单时间晚的在前排序', value: 'payAtMin desc'}]
const goodsSort = ref('')
const skuSort = ref('')
const showSortContent = ref(false)
const showTableSet = ref(false)
const goodsSortList = [
  {label: '按商品数量从大到小排序', value: 'goodsCount desc'},
  {label: '按商品数量从小到大排序', value: 'goodsCount asc'},
  {label: '按商品简称排序', value: 'goodsAlias asc'},
  {label: '按商品简称(无简称时按照标题)排序', value: 'goodsTitle asc'},
  {label: '按商品编码排序', value: 'outerIid asc'},
  {label: '按下单时间早的在前排序', value: 'payAtMin asc'},
  {label: '按下单时间晚的在前排序', value: 'payAtMin desc'},
]
const showTypeList = [
  {
    label: '按商品-规格信息统计',
    value: 1,
    columns: [{name: '商品信息', value: 'goods', disabled: true},
      {name: '规格信息', value: 'sku', disabled: true}, {
        name: '数量',
        value: 'count',
        disabled: false
      }, {
        name: '金额',
        value: 'price',
        disabled: false
      },
      {
        name: '总数',
        value: 'totalCount', disabled: false
      }, {name: '总金额', value: 'totalPrice', disabled: false}]
  },
  {
    label: '仅按规格信息统计', value: 2,
    columns: [{
      name: '规格信息',
      value: 'sku', disabled: true
    }, {name: '数量', value: 'count', disabled: false}, {
      name: '金额',
      value: 'price',
      disabled: false
    }
    ]
  },
]
const showType = ref(1)
const goodsSet = ref({})
const skuSet = ref({})
const goodsPicSize = ref(80)
const skuPicSize = ref(50)
const type2ShowColumnMap = ref({})
const beihuoTemplate = ref({})
const statObj = ref({
  tableOrderNum: 0,
  tableGoodsNum: 0,
  tableSkuNum: 0,
  tableTotal: 0,
  totalPayment: 0
})

function sortTitle() {
  const skuSortVal = skuSortList.find(it => it.value === skuSort.value)
  if (showType.value === 1) {
    const goodsSortVal = goodsSortList.find(it => it.value === goodsSort.value)
    return `先 ${goodsSortVal ? goodsSortVal.label : goodsSortList[0].label}，再 ${skuSortVal ? skuSortVal.label : skuSortList[0].label}`
  }
  return `${skuSortVal ? skuSortVal.label : skuSortList[0].label}`
}

function showColumns() {
  return showTypeList.find(it => it.value === showType.value).columns
}

const notLoadData = ref(true)

defineExpose({init});

function init(config){
  getConfig(config)
  queryDataAndStatic(false)
}

function queryDataAndStatic(load = true) {
  notLoadData.value = !load
  tableData.value = []
  statObj.value = {
    tableOrderNum: 0,
    tableGoodsNum: 0,
    tableSkuNum: 0,
    tableTotal: 0,
    totalPayment: 0
  }
  if (!load) {
    return
  }
  getData();
}

function changeSortOrShowType(itemName, value) {
  beihuoTemplate.value[itemName] = value
  saveConfig(beihuoTemplate.value, true)
  queryDataAndStatic(true)
}

function columnShow(val) {
  const columns = type2ShowColumnMap.value[showType.value] || []
  return columns.findIndex(it => it === val) > -1;
}

function changeColumnShow(val, itemName) {
  let columns = type2ShowColumnMap.value[showType.value] || []
  if (val) {
    columns.push(itemName)
  } else {
    columns = columns.filter(it => it !== itemName)
  }
  type2ShowColumnMap.value[showType.value] = columns
  beihuoTemplate.value.type2ShowColumnMap = type2ShowColumnMap.value
  saveConfig(beihuoTemplate.value, false)
}

function changeGoodsOrSkuConfig({type, fieldName, value, reload = false}) {
  beihuoTemplate.value[type][fieldName] = value
  let size = 48;
  if (fieldName === 'picSize') {
    switch (value) {
      case 1:
        size = 50;
        break;
      case 2:
      default:
        size = 80;
        break;
      case 3:
        size = 100;
        break;
    }
    if (type === 'goodsSet') {
      goodsPicSize.value = size;
    } else {
      skuPicSize.value = size;
    }
  }
  saveConfig(beihuoTemplate.value, reload)
}

function generateSkuTitle(it) {
  switch (skuSet.value.showTitleType) {
    case 1:
    default:
      return it.customSkuValue || it.skuValue
    case 2:
      return it.customSkuValue
    case 3:
      return it.skuValue
  }
}

function generateGoodsTitle(it) {
  switch (goodsSet.value.showTitleType) {
    case 1:
    default:
      return it.customTitle || it.goodsTitle
    case 2:
      return it.customTitle
    case 3:
      return it.goodsTitle
  }
}

function saveConfig(template, reload = false) {
  saveUserSetting({stock_config:JSON.stringify(template)}).then(res => {
    if (reload) {
      queryDataAndStatic(true);
    }
  });
}
 function getConfig(beihuoContents) {
  // showType: 按商品1 按规格2 ，goodsSet 商品设置 ， skuSet 规格设置  goodsSort商品排序  skuSort规格排序
  if (beihuoContents.type2ShowColumnMap) {
    type2ShowColumnMap.value = beihuoContents.type2ShowColumnMap
  } else {
    type2ShowColumnMap.value = {
      1: ['goods', 'sku', 'count', 'totalCount', 'totalPrice'],
      2: ['sku', 'count']
    }
  }
  showType.value = beihuoContents.showType || 1
  goodsSort.value = beihuoContents.goodsSort || 'goodsCount desc'
  skuSort.value = beihuoContents.skuSort || 'skuCount desc'
  goodsSet.value = beihuoContents.goodsSet || {
    picShow: true,
    picSize: 3,
    showTitleType: 1,
    showId: true,
    showOuterId: true,
    mergeSet: 1
  }
  skuSet.value = beihuoContents.skuSet || {
    picShow: true,
    picSize: 2,
    showTitleType: 1,
    showId: true,
    showOuterId: true,
    mergeSet: 1
  }
  beihuoTemplate.value = {
    showType: showType.value,
    goodsSort: goodsSort.value,
    skuSort: skuSort.value,
    goodsSet: goodsSet.value,
    skuSet: skuSet.value,
    type2ShowColumnMap: type2ShowColumnMap.value
  }
}
</script>

<template>
  <div>
    <div class="goods-picking-list">
      <div class="order-search">
        <maSearch
          :options="{ showButton: true }"
          :form-options="{ labelWidth: '80px'}"
          :search-items="searchItem"
          @search="getData"
        />
      </div>
      <div class="order-table">
        <div class="order-opt-content flex_center">
          <div class="data-summary">
            <div>
              <p>订单数</p>
              <p class="number-font">{{statObj.tableOrderNum}}</p>
            </div>
            <div>
              <p>商品种类数</p>
              <p class="number-font">{{statObj.tableGoodsNum}}</p>
            </div>
            <div>
              <p>规格种类数</p>
              <p class="number-font">{{statObj.tableSkuNum}}</p>
            </div>
            <div>
              <p>商品总件数</p>
              <p class="number-font">{{statObj.tableTotal}}</p>
            </div>
            <div>
              <p>总实付金额</p>
              <p class="number-font">{{statObj.totalPayment}}</p>
            </div>
          </div>
          <div class="ml-auto flex_center">
            <el-popover trigger="click" placement="bottom" transfer="transfer" @show="showSortContent=true"
                        @hide="showSortContent=false" :width="showType === 1 ? 600 : 350">
              <template #reference>
                <el-button>
                  <span>{{ sortTitle() }}</span>
                  <ArrowUp v-if="showSortContent" :size="4" class="ml-1"></ArrowUp>
                  <ArrowDown v-else :size="4" class="ml-1"></ArrowDown>
                </el-button>
              </template>
              <div class="sort-content">
                <div class="sort-item" v-if="showType === 1">
                  <h3>商品排序</h3>
                  <div class="sort-list">
                    <el-radio-group vertical="vertical" v-model="goodsSort"
                                    @change="changeSortOrShowType('goodsSort',goodsSort)">
                      <el-radio v-for="it in goodsSortList" :label="it.value" :key="it.value">{{ it.label }}</el-radio>
                    </el-radio-group>
                  </div>
                </div>
                <div class="sort-item" :style="showType === 2 ? 'width:100%':''">
                  <h3>规格排序</h3>
                  <div class="sort-list">
                    <el-radio-group vertical="vertical" v-model="skuSort"
                                    @change="changeSortOrShowType('skuSort',skuSort)">
                      <el-radio v-for="it in skuSortList" :label="it.value" :key="it.value">{{ it.label }}</el-radio>
                    </el-radio-group>
                  </div>
                </div>
              </div>
            </el-popover>
            <el-popover class="ml-5" trigger="click" placement="bottom" transfer="transfer" @show="showTableSet=true"
                        @hide="showTableSet=false" :width="400">
              <template #reference>
                <el-button>
                  <span>表格设置</span>
                  <ArrowUp v-if="showTableSet" :size="4" class="ml-1"></ArrowUp>
                  <ArrowDown v-else :size="4" class="ml-1"></ArrowDown>
                </el-button>
              </template>
              <div>
                <el-radio-group v-model="showType" @change="changeSortOrShowType('showType',showType)">
                  <el-radio v-for="it in showTypeList" :key="it.value" :label="it.value">{{ it.label }}</el-radio>
                </el-radio-group>
                <div class="tableColumns">
                  <el-checkbox v-for="item in showColumns()" :disabled="item.disabled"  :model-value="columnShow(item.value)"
                               @change="(e)=>changeColumnShow(e,item.value)">{{ item.name }}
                  </el-checkbox>
                </div>
              </div>
            </el-popover>
          </div>
        </div>
        <div class="table_swapper mt-3" ref="table_swapper">
          <table class="items_table" ref="table">
            <thead class="it_head2">
            <tr>
              <th class="tc" style="width:60px">序号</th>
              <th class="can_scale" v-if="columnShow('goods')" style="min-width:200px">
                <div class="flex_center">
                  <span class="mr-1">商品信息</span>
                  <GoodsSetConfig :goodsSet="goodsSet" @changeConfig="changeGoodsOrSkuConfig"/>
                </div>
              </th>
              <th class="can_scale" v-if="columnShow('sku')" style="min-width:200px">
                <div class="flex_center">
                  <span class="mr-1">规格信息</span>
                  <SkuSetConfig class="ml5" :sku-set="skuSet" @changeConfig="changeGoodsOrSkuConfig"></SkuSetConfig>
                </div>
              </th>
              <th class="can_scale fix-width" v-if="columnShow('count')" style="width:150px">数量</th>
              <th class="can_scale fix-width" v-if="columnShow('price')" style="width:200px">金额</th>
              <th class="can_scale fix-width" v-if="columnShow('totalCount')" style="width:150px">总数</th>
              <th class="can_scale fix-width" v-if="columnShow('totalPrice')" style="width:200px">总金额</th>
              <th class="fix-width remarkColumn" style="width:100px">操作</th>
            </tr>
            </thead>
            <tbody class="it_body">
            <template v-for="row in tableData">
              <tr class="order-row">
                <td style="text-align:center" :rowspan="row.rowspan" v-if="row.rowspan>0">{{ row.index }}</td>
                <td v-if="columnShow('goods') && row.rowspan>0" :rowspan="row.rowspan">
                  <div class="flex_top" style="white-space: break-spaces;">
                    <div class="goods-img-info mr10" v-if="goodsSet.picShow">
                      <div class="goods-popover" v-popover="'bottom-start'">
                        <div class="flex_container img-wrap"
                             :style="{'width':goodsPicSize +'px','height':goodsPicSize +'px'}"><img
                          :src="row.goodsPic_200" :style="{'width':goodsPicSize +'px','height':goodsPicSize +'px'}"/>
                        </div>
                        <div class="bigpic pic-s" slot="content"><img class="img_bigpic" :src="row.goodsPic_200"/></div>
                      </div>
                    </div>
                    <div class="goods-info">
                      <div class="info">
                        <p>{{ generateGoodsTitle(row) }}</p>
                        <p v-if="goodsSet.showId">商品ID：
                          <copy-text :value="row.numIid"></copy-text>
                        </p>
                        <p v-if="goodsSet.showOuterId">商品编码：
                          <copy-text :value="row.outIid" v-if="row.outIid"></copy-text>
                        </p>
                        <Poptip placement="bottom" v-model="row.editCustomTitle" :transfer="true">
                          <div class="goodsAlias">
                            <Icon class="a-hover modify_icon hover_show icon-edit blue"
                                  type="ios-create-outline"></Icon>
                            <a>修改商品简称</a>
                          </div>
                          <div slot="content">
                            <Input v-model="row.customTitle"></Input>
                            <div class="tr mt8">
                              <Button class="mr8" size="small" @click="row.editCustomTitle = false">取消</Button>
                              <Button type="primary" size="small" @click="submitEditCustomTitle(row)">确定</Button>
                            </div>
                          </div>
                        </Poptip>
                      </div>
                    </div>
                  </div>
                </td>
                <td v-if="columnShow('sku')">
                  <div class="flex_top" style="white-space: break-spaces;">
                    <div class="goods-img-info mr10" v-if="skuSet.picShow">
                      <div class="goods-popover" v-popover="'bottom-start'">
                        <div class="flex_container img-wrap"
                             :style="{'width':skuPicSize +'px','height':skuPicSize +'px'}"><img :src="row.sku_pic_200"
                                                                                                :style="{'width':skuPicSize +'px','height':skuPicSize +'px'}"/>
                        </div>
                        <div class="bigpic pic-s" slot="content"><img class="img_bigpic" :src="row.sku_pic_200"/></div>
                      </div>
                    </div>
                    <div class="goods-info">
                      <div class="info">
                        <p>{{ generateSkuTitle(row) }}</p>
                        <p v-if="skuSet.showId">规格ID：
                          <copy-text :value="row.skuId"></copy-text>
                        </p>
                        <p v-if="skuSet.showOuterId">规格编码：
                          <copy-text :value="row.skuOutIid" v-if="row.skuOutIid"></copy-text>
                        </p>
                        <Poptip placement="bottom" v-model="row.editSkuCustomTitle" :transfer="true">
                          <div class="goodsAlias">
                            <Icon class="a-hover modify_icon hover_show icon-edit blue"
                                  type="ios-create-outline"></Icon>
                            <a>修改规格简称</a>
                          </div>
                          <div slot="content">
                            <Input v-model="row.customSkuValue"></Input>
                            <div class="tr mt8">
                              <Button class="mr8" size="small" @click="row.editSkuCustomTitle = false">取消</Button>
                              <Button type="primary" size="small" @click="submitEditSkuCustomTitle(row)">确定</Button>
                            </div>
                          </div>
                        </Poptip>
                      </div>
                    </div>
                  </div>
                </td>
                <td class="tc fix-width" v-if="columnShow('count')"> {{ row.num }}</td>
                <td class="tc fix-width" v-if="columnShow('price')"> {{ row.skuPayment }}</td>
                <td class="tc fix-width" v-if="columnShow('totalCount') && row.rowspan>0" :rowspan="row.rowspan">
                  {{ row.totalNum }}
                </td>
                <td class="tc fix-width" v-if="columnShow('totalPrice') && row.rowspan>0" :rowspan="row.rowspan">
                  {{ row.payment }}
                </td>
                <td class="tc fix-width remarkColumn" v-if="row.rowspan>0" :rowspan="row.rowspan"><a
                  @click="showRemark(row)">{{ row.remarkShow ? '隐藏备注' : '显示备注' }}</a></td>
              </tr>
              <tr v-if="row.remarkShow && row.lastObj">
                <td :colspan="remarkColspan">
                  <div class="remark-list" v-if="row.remarkList.length">
                    <div class="remark-item mb5" v-for="it in row.remarkList"><span>订单编号：{{ it.tid }}</span><span
                      class="ml20">卖家备注：
                  <svg-icon icon-class="flag" :class-name="'dy_flag_' + it.sellerFlag"></svg-icon><span
                        class="ml5">{{ it.sellerMemo }}</span></span><span class="ml20"
                                                                           v-if="it.buyerMemo">买家留言：{{ it.buyerMemo }}</span>
                    </div>
                  </div>
                  <span v-else>暂无备注</span>
                </td>
              </tr>
            </template>
            </tbody>
          </table>
          <el-empty description="请点击查询按钮生成备货单" v-if="notLoadData"/>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss">
.sort-content {
  font-size: 13px;
  display: flex;
  align-items: start;
  padding: 20px 10px;

  .sort-item {
    width: 48%;
    margin-left: 10px;

    .sort-list {
      border-radius: 4px;
      background-color: #f0f2f5;
      padding: 0 10px;
      margin-top: 5px;
    }
  }
}

.tableColumns {
  margin: 10px 0;
  background-color: #f0f2f5;
  border-radius: 4px;
  padding: 5px;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
}

.goods-picking-list {

  .expand-content {
    padding: 0.5rem;

    .right_price {
      width: 120px;
      border-left: 1px solid #ccc;
      margin-left: auto;
      padding-left: 1rem;
    }
  }

  .order-search {
    background: white;
    padding: 0.75rem 0.75rem 0 0.75rem;
    margin: 0.75rem 0;
  }

  .order-table {
    background: white;
    padding: 0 0.75rem 5rem 0.75rem;
    margin: 0.75rem 0;

    .order-opt-content {
      height: 60px;
      line-height: 30px;
      font-size: 14px;
    }
    .data-summary {
      display: grid;
      text-align: center;
      grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
      width: calc(100% - 600px);
      .number-font {
        text-align: center;
        color: #1890ff;
        font-size: 25px;
      }
    }

    table.items_table {
      width: 100%;
      table-layout: fixed;

      tr th, tr td {
        padding: 9px 6px;
        border-bottom: 1px solid #e8eaec;
        border-top: 1px solid #e8eaec;
        max-width: calc(12.5% - 10px);
        word-break: break-all;
        line-height: 1.2;
      }

      .it_head2 th {
        background-color: #f8f8f9;
        text-align: left;

        &:first-child {
          padding: 9px 18px;
        }
      }

      .it_head2 .user_flag {
        width: 50px;
      }

      .it_body tr td {
        font-size: 12px;
        text-align: left;
        vertical-align: top;
        line-height: 18px;

        &.merge_td:nth-child(n + 1) {
          border-bottom: none;
          border-top: none;
        }

        &.merge_main_td {
          border-bottom: none;
        }

        &.no_bottom_padding {
          padding-bottom: 0;
        }
      }

      .it_body tr .ops > * {
        margin-right: 10px;
        display: inline-block;

        &:last-child {
          margin-right: 0;
        }
      }

      .it_body tr .edit_remark button {
        margin: 5px 5px 0 0;
      }

      .it_body tr:last-child td.merge_td {
        border-bottom: 1px solid #e8eaec;
      }

      .it_body tr.main_order td:first-child {
        padding: 9px 6px;
      }

      .it_body tr.order_isOperated {
        background-color: rgba(220, 222, 226, 0.722);
      }

      .it_body .good_sku_wrapper .c_img {
        width: 40px;
        height: 40px;
      }

      .it_body td.left_top {
        vertical-align: top;
        text-align: left;
      }

      .pic-s {
        min-height: 217px;

        img {
          width: 200px;
          height: 200px;
          border: 1px solid #e8eaec;
        }

        .inner_content {
          max-width: 200px;
          white-space: normal;
        }
      }

      .row_ellipse {
        line-height: 18px;
        display: block;
      }

      .unSendTag {
        background-color: #46a6ff;
        border: 1px solid #46a6ff;
      }

      .order_closed_tag {
        background-color: #ed4014;
        border: 1px solid #ed4014;
      }

      .partSendTag {
        background-color: rgba(24, 157, 151, 0.749);
      }

      .sendTag {
        background-color: #67c23a;
      }

      .common_tag {
        color: #27c24e;
        border: 1px solid rgba(39, 194, 78, 0.271);
        background-color: rgba(103, 194, 58, 0.161);

        &2 {
          line-height: 1.2;
        }
      }

      .printed {
        color: #67c23a;
        border: 1px solid #67c23a;
        background-color: #fff;
      }

      .unPrinted {
        color: #909399;
        border: 1px solid #bfbfbf;
        background-color: #fff;
      }

      .error_tag {
        width: 16px;
        height: 16px;
        text-align: center;
        line-height: 16px;
        border-radius: 8px;
        margin-right: 5px;
        background-color: #f40;
        color: #fff;
      }

      .tag {
        padding: 2px 8px;
        border-radius: 3px;
        display: inline-block;
        margin-bottom: 2px;
        user-select: none;
        line-height: 1.2;
        font-size: 12px;

        &.error {
          background-color: #ff4500;
          color: #fff;

          &-1 {
            background-color: #fff;
            border: 1px solid #ff4500;
            color: #ff4500;
          }
        }

        &.part_error {
          color: #ff4500;
          border: 1px solid #ff4500;
        }

        &.part_success {
          color: #19be6b;
          border: 1px solid #19be6b;
        }

        &.success {
          color: #fff;
          background-color: #19be6b;
        }

        &.default {
          color: #808695;
          border: 1px solid #c5c8ce;
          background-color: #f8f8f9;
        }

        &.merge_tag {
          background-color: #a307be;
          color: #fff;
        }

        &.like_tag {
          background-color: #f90;
          color: #fff;
        }

        &.part_tag {
          background-color: rgba(24, 157, 151, 0.749);
        }

        &.can_merge_tag {
          background-color: #50b52f;
          color: #fff;
        }

        &.village_tag {
          color: #1890ff;
          border: 1px solid #1890ff;
          background-color: #fff;
          padding: 0 4px;
        }
      }

      .locked a {
        color: #d9d9d9;
      }

      .locked .can_op {
        color: #1c87f7;
      }

      .can_scale {
        min-width: 200px;
      }

      .disabled {
        color: #d9d9d9;
      }

      .show-expand {
        border-right: 1px solid #e0e1e2;
        padding-right: 10px;
      }
    }

    .table_swapper {
      padding: 0 8px 30px;

      .loading {
        padding: 12px;
        text-align: center;
        color: #BFBFBF;
        font-weight: bolder;
        font-size: 16px;
      }
    }
  }

  .tab-select {
    background: white;
    padding: 0.75rem;
  }

  .opt-button {
    position: absolute;
    right: 1rem;
    top: 0.75rem;
  }
}
</style>
