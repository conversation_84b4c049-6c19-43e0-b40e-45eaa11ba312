<script setup>
import {getUserSetting} from "~/base/api/user.js";
import GoodsCheckAccount from "~/base/views/statistic/goodsCheckAccount.vue";
import ProductCheckAccount from "~/base/views/statistic/productCheckAccount.vue";



const deliveryTemplate = ref({})

onMounted(async () => {
  await getConfig()
  changeType()
})



async function getConfig() {
  const res = await getUserSetting()
  let deliveryContents = {}
  if (res.data?.delivery_config) {
    deliveryContents = JSON.parse(res.data.delivery_config)
  }
  deliveryTemplate.value = deliveryContents
}

const groupType = ref(1)
const typeList = [
  {label: '平台商品维度', value: 1},
  {label: '系统货品维度', value: 2}
]

const goodsCheckAccountRef =ref()
const productCheckAccountRef =ref()

function changeType(){
  switch (groupType.value) {
    case 1:
      goodsCheckAccountRef.value.init(deliveryTemplate['goods'] || {})
      break;
    case 2:
      productCheckAccountRef.value.init(deliveryTemplate['product'] || {})
      break;
    default:
      break;
  }
}


</script>

<template>
  <div>
    <div class="order-check-account">
      <el-radio-group v-model="groupType" @change="changeType">
        <el-radio-button v-for="it in typeList" :key="it.value" :value="it.value" :label="it.label"/>
      </el-radio-group>
      <goods-check-account v-if="groupType === 1" ref="goodsCheckAccountRef"/>
      <product-check-account v-if="groupType === 2" ref="productCheckAccountRef"/>
    </div>
  </div>
</template>

<style lang="scss">
.order-check-account {
  margin: 0.75rem;
}
</style>
