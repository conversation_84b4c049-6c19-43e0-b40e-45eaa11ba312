<script setup>
import {ArrowDown, ArrowUp} from '@element-plus/icons-vue'
import {cloneDeep} from "lodash-es";

const visible = ref(false)
const quickSearch = ref(null)
const selectRule = ref({
  type: 1,
  list: []
})
const skuList = [
  {
    skuId: 1,
    skuImg: 'https://img.alicdn.com/bao/uploaded/i1/75758527/O1CN01eRU5xd2CrQKf0bxlG_!!75758527.jpg',
    title: '绿色树叶【802福利】70桑蚕丝30天丝提花织锦缎直筒女士长裤',
    count: 67,
  },
  {
    skuId: 2,
    skuImg: 'https://img.alicdn.com/bao/uploaded/i4/75758527/O1CN01M7oqzU2CrQKf5CLls_!!75758527.jpg',
    title: '紫色树叶【802福利】70桑蚕丝30天丝提花织锦缎直筒女士长裤',
    count: 56,
  },
  {
    skuId: 3,
    skuImg: 'https://img.alicdn.com/bao/uploaded/i1/75758527/O1CN01MkVaAx2CrQKf53QPu_!!75758527.jpg',
    title: '蓝色花朵【802福利】70桑蚕丝30天丝提花织锦缎直筒女士长裤',
    count: 53,
  },
  {
    skuId: 4,
    skuImg: 'https://img.alicdn.com/bao/uploaded/i4/75758527/O1CN01pX5FTj2CrQKfcpt5b_!!75758527.jpg',
    title: '【804福利J1030】100桑蚕丝衬衫领加捻丝门襟带褶纽扣上衣',
    count: 49,
  },
  {
    skuId: 5,
    skuImg: 'https://img.alicdn.com/bao/uploaded/i1/75758527/O1CN01CMUu822CrQKYOGVfB_!!75758527.jpg',
    title: '蓝底碎花【728福利】100桑蚕丝42针120克V领短袖睡裙连衣裙',
    count: 44,
  },
  {
    skuId: 6,
    skuImg: 'https://img.alicdn.com/bao/uploaded/i2/75758527/O1CN01AFKbcO2CrQKfbMBSE_!!75758527.jpg',
    title: 'B32【414直播福利】100桑蚕丝纯色网纱女士圆领背心',
    count: 37,
  },
  {
    skuId: 7,
    skuImg: 'https://img.alicdn.com/bao/uploaded/i2/75758527/O1CN01UMf1Sl2CrQIF4ZZmT_!!75758527.jpg',
    title: 'I54【315直播福利】100桑蚕丝42针猫咪家居睡衣套装',
    count: 28,
  },
  {
    skuId: 8,
    skuImg: 'https://img.alicdn.com/bao/uploaded/i2/75758527/O1CN016VnJyG2CrQHNNdiQo_!!75758527.jpg',
    title: '5个装【109YY福利】桑蚕丝纯色收纳袋手机袋',
    count: 23,
  },
  {
    skuId: 9,
    skuImg: 'https://img.alicdn.com/bao/uploaded/i4/75758527/O1CN01PwUs6T2CrQC4XnYR5_!!75758527.jpg',
    title: 'C43暗花纹【414直播福利】桑蚕丝混纺纯色休闲裤女士裙裤九分裤',
    count: 6,
  },
]
const currentSkuList = ref(cloneDeep(skuList))


const popoverInputRef = ref()
const popoverContentRef = ref()

const handleClickOutside = (event) => {
  // 检查点击是否发生在 input 或 popover 内容内部
  const isClickInsideInput = popoverInputRef.value?.$el.contains(event.target)
  const isClickInsidePopover = popoverContentRef.value?.contains(event.target)
  if (!isClickInsideInput && !isClickInsidePopover) {
    visible.value = false
  } else {
    visible.value = true
  }
}

const selectAllFlag = computed(()=>{
  return selectRule.value.list?.length === currentSkuList.value.length
})

function selectAll(e) {
  if(e) {
    //全选
    selectRule.value.list = cloneDeep(currentSkuList.value)
  } else {
    selectRule.value.list = []
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})

</script>

<template>
  <div class="goods-filter">
    <el-popover trigger="click" :offset="0" :show-arrow="false" placement="bottom" v-model="visible"
                :width="400" popper-class="goods-filter-popover">
      <template #reference>
        <el-input ref="popoverInputRef" :suffix-icon="visible ? ArrowUp : ArrowDown" placeholder="商品名/商品ID/简称/编码 规格名/简称/编码"
                  v-model="quickSearch"/>
      </template>
      <div ref="popoverContentRef">
        <el-radio-group v-model="selectRule.type" class="mb-2">
          <el-radio-button :value="1">包含</el-radio-button>
          <el-radio-button :value="2">排除</el-radio-button>
        </el-radio-group>
        <el-checkbox-group v-model="selectRule.list" class="sku-list">
          <div class="flex_center mb-2" v-for="it in currentSkuList" :key="it.skuId">
            <el-checkbox :value="it.skuId" />
            <img style="width: 50px; height: 50px;min-width: 50px" :src="it.skuImg"/>
            <span class="ml-2 title">
              {{it.title}}
            </span>
            <span class="ml-auto mr-2" style="width:80px;text-align:right">
              {{it.count}}件
            </span>
          </div>
        </el-checkbox-group>
        <div class="mt-5">
          <el-checkbox :model-value="selectAllFlag" @change="selectAll">全选（{{selectRule.list.length}}/{{currentSkuList.length}}）</el-checkbox>
        </div>


      </div>

    </el-popover>
  </div>


</template>

<style lang="scss">
.goods-filter {
  .select-input {
    align-items: center;
    background-color: var(--el-fill-color-blank);
    border-radius: var(--el-border-radius-base);
    box-shadow: 0 0 0 1px var(--el-border-color) inset;
    box-sizing: border-box;
    cursor: pointer;
    display: flex;
    font-size: 14px;
    gap: 6px;
    line-height: 24px;
    min-height: 32px;
    padding: 4px 12px;
    position: relative;
    text-align: left;
    transform: translateZ(0);
    transition: var(--el-transition-duration);
  }
}

.goods-filter-popover {
  .el-input-group__append {
    background-color: var(--el-color-primary) !important;
    color: white !important;
  }

  .el-checkbox-group{
    font-size: unset;
    line-height: unset;
  }

  .title {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .sku-list {
    height: 300px;
    overflow-y: auto;
  }
}
</style>
