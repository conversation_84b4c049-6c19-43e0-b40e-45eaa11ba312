<script setup>
const props = defineProps({
  form: {
    type: Object,
    default: {
      skuInclude: '1'
    },
  }
});
const goodsCombinOption = [
  {
    value: "1",
    label: "规格模糊搜索(包含)",
    desc: '<span>支持分别输入规格相关字段，搜索<span class="red">包含</span>关键词的结果</span>'
  },
  {
    value: "2",
    label: "规格精确搜索(等于)",
    desc: '<span>支持分别输入规格相关字段，搜索<span class="red">等于</span>关键词的结果</span>'
  },
  {
    value: "3",
    label: "规格精确搜索(不等于)",
    desc: '<span>支持分别输入规格相关字段，搜索<span class="red">不等于</span>关键词的结果</span>'
  },
]
</script>

<template>
  <div class="sku-combin-search flex_center">
    <el-select class="right_angle" v-model="props.form.skuInclude" style="min-width:150px;max-width:150px" popper-class="combin-search">
      <el-option v-for="item in goodsCombinOption" :value="item.value" :key="item.value" :label="item.label">
        <p class="bold_font">{{ item.label }}</p>
        <p v-html="item.desc"></p>
      </el-option>
    </el-select>
    <div class="flex_center" style="width:100%" >
      <el-input class="left_angle1 right_angle" v-model.trim="props.form.sku_value" placeholder="规格名称" clearable />
      <el-input class="left_angle right_angle" v-model.trim="props.form.custom_sku_value" placeholder="规格简称" clearable />
      <el-input class="left_angle right_angle" v-model.trim="props.form.outer_sku_iid" placeholder="规格编码" clearable></el-input>
      <el-input class="left_angle right_angle" v-model.trim="props.form.skuValue1" placeholder="规格属性1" clearable/>
      <el-input class="left_angle" v-model.trim="props.form.skuValue2" placeholder="规格属性2" clearable/>
    </div>
  </div>
</template>

<style lang="scss">
.combin-search {
  .el-select-dropdown__item {
    height: 50px !important;
    line-height: 25px !important;
  }
}
.sku-combin-search {
  .right_angle .el-select__wrapper, .right_angle .el-input__wrapper {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }
  .left_angle1 {
    width: 25%;
    .el-input__wrapper, .el-select__wrapper {
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
    }
  }
  .left_angle {
    width: 25%;
    .el-input__wrapper, .el-select__wrapper {
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
      border-left: 0;
    }
  }
  .el-input, .el-select {
    margin-left: -1px;
  }
}

</style>
